-- ADMIN SETUP - CREATE ADMIN USERS
-- Use this to make any user an admin (replace the email with actual user email)

-- Step 1: Show all current users and their roles
SELECT 'Current Users and Roles:' as info;
SELECT 
  up.username,
  up.full_name,
  up.role,
  au.email,
  up.created_at,
  CASE 
    WHEN up.role = 'admin' THEN '👑 ADMIN ACCESS'
    WHEN up.role = 'editor' THEN '✏️ EDITOR ACCESS' 
    WHEN up.role = 'writer' THEN '📝 WRITER ACCESS'
    ELSE '👤 USER ACCESS' 
  END as access_level
FROM user_profiles up
JOIN auth.users au ON up.id = au.id
ORDER BY up.created_at;

-- Step 2: Make a user admin by email (REPLACE WITH ACTUAL EMAIL)
-- Example: UPDATE user_profiles SET role = 'admin' WHERE id = (SELECT id FROM auth.users WHERE email = '<EMAIL>');

UPDATE user_profiles 
SET role = 'admin' 
WHERE id = (
  SELECT id FROM auth.users WHERE email = '<EMAIL>'  -- Replace with your email
);

-- Step 3: Verify admin was created
SELECT 'Admin Users:' as info;
SELECT 
  up.username,
  up.full_name,
  up.role,
  au.email,
  '👑 HAS ADMIN ACCESS' as status
FROM user_profiles up
JOIN auth.users au ON up.id = au.id
WHERE up.role = 'admin';

-- Step 4: Show role distribution
SELECT 'Role Distribution:' as info;
SELECT 
  role,
  COUNT(*) as user_count,
  CASE 
    WHEN role = 'admin' THEN '👑'
    WHEN role = 'editor' THEN '✏️' 
    WHEN role = 'writer' THEN '📝'
    ELSE '👤' 
  END as icon
FROM user_profiles 
GROUP BY role
ORDER BY 
  CASE role 
    WHEN 'admin' THEN 1
    WHEN 'editor' THEN 2
    WHEN 'writer' THEN 3
    ELSE 4
  END;

-- Success message
SELECT '✅ ADMIN SETUP COMPLETE!' as result;
SELECT 'Users with admin role can now manage the entire system!' as info;
