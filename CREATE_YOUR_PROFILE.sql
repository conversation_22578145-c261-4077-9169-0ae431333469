-- CREATE YOUR MISSING PROFILE AND SET AS ADMIN
-- Run this AFTER running the main schema to fix your specific issue

-- Step 1: Check if your profile exists
SELECT 'Checking if profile exists...' as status;
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM user_profiles WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0') 
    THEN '✅ Profile EXISTS' 
    ELSE '❌ Profile MISSING - Will create it' 
  END as profile_status;

-- Step 2: Create your missing profile (this fixes your Auth Debug showing null values)
INSERT INTO user_profiles (id, username, full_name, avatar_url, role)
SELECT 
  '8973ff45-fcf5-4b50-93f8-4068653efda0',
  'p775917',  -- username from your email
  'Admin User',  -- you can change this later in the app
  'https://api.dicebear.com/7.x/avataaars/svg?seed=8973ff45-fcf5-4b50-93f8-4068653efda0',
  'admin'
WHERE NOT EXISTS (
  SELECT 1 FROM user_profiles WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0'
);

-- Step 3: Make sure your profile is set to admin (in case it already existed)
UPDATE user_profiles 
SET role = 'admin' 
WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';

-- Step 4: Verify your profile is now created and working
SELECT '🎉 YOUR PROFILE STATUS:' as status;
SELECT 
  id,
  username,
  full_name,
  role,
  created_at,
  CASE 
    WHEN role IN ('admin', 'editor') THEN '✅ HAS ADMIN ACCESS' 
    ELSE '❌ NO ADMIN ACCESS' 
  END as admin_status
FROM user_profiles 
WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';

-- Success messages
SELECT '✅ YOUR PROFILE HAS BEEN CREATED!' as result;
SELECT 'Now refresh your app and the Auth Debug should show your profile details!' as instruction;
SELECT 'You should now have admin access to create articles and manage the site!' as access_info;
