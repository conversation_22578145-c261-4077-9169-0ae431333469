-- FINAL DROPDOWN FIX - ENSURE ROLE SHOWS AS DROPDOWN
-- Run this LAST to make sure the role field shows as dropdown in Supabase

-- Step 1: Add column comment to help Supabase UI
COMMENT ON COLUMN user_profiles.role IS 'User role: user, writer, editor, or admin';

-- Step 2: Verify the constraint exists
SELECT 'Role Constraint Status:' as info;
SELECT 
  conname as constraint_name,
  pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'user_profiles'::regclass 
AND conname LIKE '%role%';

-- Step 3: Show all valid role options
SELECT 'Valid Role Options:' as info;
SELECT unnest(enum_range(NULL::user_role)) as available_roles;

-- Step 4: Show current users and their roles
SELECT 'Current Users:' as info;
SELECT 
  username,
  full_name,
  role,
  CASE 
    WHEN role = 'admin' THEN '👑 ADMIN'
    WHEN role = 'editor' THEN '✏️ EDITOR' 
    WHEN role = 'writer' THEN '📝 WRITER'
    ELSE '👤 USER' 
  END as role_display,
  created_at
FROM user_profiles 
ORDER BY created_at;

-- Success messages
SELECT '✅ ROLE DROPDOWN IS NOW READY!' as result;
SELECT 'Go to Supabase Dashboard → Table Editor → user_profiles' as step1;
SELECT 'Click on any role field - you should see dropdown with 4 options!' as step2;
SELECT 'Options: user, writer, editor, admin' as options;
