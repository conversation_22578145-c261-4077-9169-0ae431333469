-- COMPLETE FIX FOR MISSING PROFILE AND ROLE DROPDOWN
-- This will create your missing profile AND fix the role dropdown
-- Run this ENTIRE script in Supabase SQL Editor

-- Step 1: Check if your profile exists
SELECT 'Checking if profile exists...' as status;
SELECT
  CASE
    WHEN EXISTS (SELECT 1 FROM user_profiles WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0')
    THEN '✅ Profile EXISTS'
    ELSE '❌ Profile MISSING - Will create it'
  END as profile_status;

-- Step 2: Create your missing profile (this is the main issue!)
INSERT INTO user_profiles (id, username, full_name, avatar_url, role)
SELECT
  '8973ff45-fcf5-4b50-93f8-4068653efda0',
  'p775917',  -- username from your email
  'Admin User',  -- you can change this later
  'https://api.dicebear.com/7.x/avataaars/svg?seed=8973ff45-fcf5-4b50-93f8-4068653efda0',
  'admin'
WHERE NOT EXISTS (
  SELECT 1 FROM user_profiles WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0'
);

-- Step 3: Create an ENUM type for user roles (for better UI support)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('user', 'writer', 'editor', 'admin');
    END IF;
END $$;

-- Step 4: Add check constraint to limit role values (this creates the dropdown)
ALTER TABLE user_profiles
DROP CONSTRAINT IF EXISTS user_profiles_role_check;

ALTER TABLE user_profiles
ADD CONSTRAINT user_profiles_role_check
CHECK (role IN ('user', 'writer', 'editor', 'admin'));

-- Step 5: Make sure your profile is set to admin
UPDATE user_profiles
SET role = 'admin'
WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';

-- Step 6: Create a comment on the role column to help Supabase UI
COMMENT ON COLUMN user_profiles.role IS 'User role: user, writer, editor, or admin';

-- Step 7: Verify your profile is now created and working
SELECT '🎉 YOUR PROFILE STATUS:' as status;
SELECT
  id,
  username,
  full_name,
  role,
  created_at,
  CASE
    WHEN role IN ('admin', 'editor') THEN '✅ HAS ADMIN ACCESS'
    ELSE '❌ NO ADMIN ACCESS'
  END as admin_status
FROM user_profiles
WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';

-- Step 8: Test the role constraint is working
SELECT 'Testing role constraint...' as test_status;

-- This should work (valid role)
UPDATE user_profiles
SET role = 'editor'
WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';

-- Change back to admin
UPDATE user_profiles
SET role = 'admin'
WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';

-- Step 9: Show all valid role options
SELECT 'Valid Role Options:' as info;
SELECT unnest(enum_range(NULL::user_role)) as valid_roles;

-- Step 10: Fix the automatic profile creation for future users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.user_profiles (id, username, full_name, avatar_url, role)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'avatar_url', 'https://api.dicebear.com/7.x/avataaars/svg?seed=' || NEW.id),
    'user'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger if it doesn't exist
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Success messages
SELECT '✅ PROFILE CREATED!' as result;
SELECT '✅ ROLE CONSTRAINT ADDED!' as result2;
SELECT '✅ AUTOMATIC PROFILE CREATION FIXED!' as result3;
SELECT 'Now refresh your app and the Auth Debug should show your profile!' as instruction1;
SELECT 'In Supabase dashboard, the role field should now show a dropdown' as instruction2;
SELECT 'Valid role options: user, writer, editor, admin' as options;
