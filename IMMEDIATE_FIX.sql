-- IMMEDIATE FIX FOR AUTHENTICATION AND <PERSON><PERSON><PERSON>LE ISSUES
-- Run this ENTIRE script in Supabase SQL Editor

-- Step 1: Fix RLS policies for user_profiles
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON user_profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all profiles" ON user_profiles;

-- Create new comprehensive policies
CREATE POLICY "Users can view own profile" ON user_profiles 
FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON user_profiles 
FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON user_profiles 
FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Admins can manage all profiles" ON user_profiles 
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role IN ('admin', 'editor')
  )
);

-- Step 2: Create your specific profile (using your CURRENT user ID from debug panel)
INSERT INTO user_profiles (id, username, full_name, avatar_url, role)
VALUES (
  '8973ff45-fcf5-4b50-93f8-4068653efda0',
  'p775917',
  'User P775917',
  'https://api.dicebear.com/7.x/avataaars/svg?seed=8973ff45-fcf5-4b50-93f8-4068653efda0',
  'admin'
) ON CONFLICT (id) DO UPDATE SET
  role = 'admin',
  username = 'p775917',
  full_name = 'User P775917';

-- Step 3: Create automatic profile creation function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.user_profiles (id, username, full_name, avatar_url, role)
  VALUES (
    new.id,
    COALESCE(new.raw_user_meta_data->>'username', split_part(new.email, '@', 1)),
    COALESCE(new.raw_user_meta_data->>'full_name', split_part(new.email, '@', 1)),
    COALESCE(new.raw_user_meta_data->>'avatar_url', 'https://api.dicebear.com/7.x/avataaars/svg?seed=' || new.id),
    'user'
  );
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 4: Create trigger for automatic profile creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Step 5: Create categories table if not exists
CREATE TABLE IF NOT EXISTS categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    slug VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#D32F2F',
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 6: Insert default categories
INSERT INTO categories (name, slug, description, color, sort_order) VALUES
('Politics', 'politics', 'Political news and analysis', '#1976D2', 1),
('Tech', 'tech', 'Technology and innovation news', '#388E3C', 2),
('World', 'world', 'International news and events', '#F57C00', 3),
('Sports', 'sports', 'Sports news and updates', '#7B1FA2', 4),
('Entertainment', 'entertainment', 'Entertainment and celebrity news', '#C2185B', 5)
ON CONFLICT (slug) DO NOTHING;

-- Step 7: Enable RLS on categories
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Step 8: Create RLS policies for categories
DROP POLICY IF EXISTS "Anyone can view active categories" ON categories;
DROP POLICY IF EXISTS "Admins can manage categories" ON categories;

CREATE POLICY "Anyone can view active categories" ON categories FOR SELECT USING (is_active = true);
CREATE POLICY "Admins can manage categories" ON categories FOR ALL USING (
    EXISTS (
        SELECT 1 FROM user_profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'editor')
    )
);

-- Step 9: Verify the setup
SELECT 'Verification Results:' as status;

SELECT 'Your Profile:' as check_type, 
       id, username, full_name, role, created_at
FROM user_profiles 
WHERE id = 'd8dad92e-6d3a-471e-b9a3-213ee1d95dac';

SELECT 'All Profiles:' as check_type, 
       COUNT(*) as total_profiles,
       COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_count
FROM user_profiles;

SELECT 'Categories:' as check_type, 
       COUNT(*) as total_categories
FROM categories;

-- Success message
SELECT '✅ SETUP COMPLETE! Your profile should now work properly.' as result;
SELECT '🔄 Please refresh your browser and try logging in again.' as next_step;
