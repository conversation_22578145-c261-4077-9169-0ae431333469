-- Q<PERSON>CK PROFILE FIX FOR YOUR SPECIFIC USER ID
-- Run this in Supabase SQL Editor

-- Step 1: Create/Update your profile with admin role
INSERT INTO user_profiles (id, username, full_name, avatar_url, role) 
VALUES (
  '8973ff45-fcf5-4b50-93f8-4068653efda0', 
  'p775917', 
  'Admin User', 
  'https://api.dicebear.com/7.x/avataaars/svg?seed=8973ff45-fcf5-4b50-93f8-4068653efda0',
  'admin'
) ON CONFLICT (id) DO UPDATE SET 
  role = 'admin',
  username = 'p775917',
  full_name = 'Admin User',
  updated_at = NOW();

-- Step 2: Verify the profile was created/updated
SELECT 'Your Profile:' as status, 
       id, username, full_name, role, created_at, updated_at
FROM user_profiles 
WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';

-- Step 3: Check RLS policies are working
SELECT 'RLS Check:' as status, 
       'Profile should be visible' as message;

-- Success message
SELECT '✅ Profile updated! Now refresh your browser and clear cache.' as result;
