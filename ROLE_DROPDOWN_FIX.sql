-- FIX ROLE DROPDOWN IN SUPABASE DASHBOARD
-- Run this to make the role field show as dropdown instead of text input

-- Step 1: Add column comment to help Supabase UI understand this is an enum
COMMENT ON COLUMN user_profiles.role IS 'User role: user, writer, editor, or admin';

-- Step 2: Show current role constraint
SELECT 'Current role constraint:' as info;
SELECT 
  conname as constraint_name,
  pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'user_profiles'::regclass 
AND conname LIKE '%role%';

-- Step 3: Test that the constraint is working properly
SELECT 'Testing role constraint...' as test_status;

-- This should work (valid role)
DO $$
BEGIN
  -- Test with a temporary update
  UPDATE user_profiles 
  SET role = 'editor' 
  WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';
  
  -- Change back to admin
  UPDATE user_profiles 
  SET role = 'admin' 
  WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';
  
  RAISE NOTICE 'Role constraint test passed!';
EXCEPTION
  WHEN check_violation THEN
    RAISE NOTICE 'Role constraint is working - invalid roles are blocked';
END $$;

-- Step 4: Show all valid role options
SELECT 'Valid Role Options:' as info;
SELECT unnest(enum_range(NULL::user_role)) as valid_roles;

-- Step 5: Show current user profiles and their roles
SELECT 'Current User Profiles:' as info;
SELECT 
  username,
  full_name,
  role,
  CASE 
    WHEN role IN ('admin', 'editor') THEN '✅ ADMIN' 
    ELSE '👤 USER' 
  END as access_level
FROM user_profiles 
ORDER BY created_at;

-- Success messages
SELECT '✅ ROLE DROPDOWN SHOULD NOW WORK!' as result;
SELECT 'Go to Supabase Dashboard → Table Editor → user_profiles' as step1;
SELECT 'Click on any role field - it should show a dropdown with: user, writer, editor, admin' as step2;
SELECT 'If you still see a text input, try refreshing the Supabase dashboard' as troubleshoot;
