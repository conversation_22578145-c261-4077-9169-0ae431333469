-- COMPLETE ROLE UPDATE FIX
-- This will fix the issue where role changes in Supabase don't reflect in the app
-- Run this ENTIRE script in Supabase SQL Editor

-- Step 1: Drop all existing RLS policies to start fresh
DROP POLICY IF EXISTS "Users can view own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON user_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON user_profiles;

-- Step 2: Create simple, working RLS policies
-- Allow users to view their own profile
CREATE POLICY "Enable read access for users based on user_id" ON user_profiles
FOR SELECT USING (auth.uid() = id);

-- Allow users to insert their own profile
CREATE POLICY "Enable insert for users based on user_id" ON user_profiles
FOR INSERT WITH CHECK (auth.uid() = id);

-- Allow users to update their own profile
CREATE POLICY "Enable update for users based on user_id" ON user_profiles
FOR UPDATE USING (auth.uid() = id);

-- Allow admins to do everything (but this won't block regular users)
CREATE POLICY "Enable all access for admins" ON user_profiles
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role IN ('admin', 'editor')
  )
);

-- Step 3: Create/Update your specific profile with admin role
INSERT INTO user_profiles (id, username, full_name, avatar_url, role) 
VALUES (
  '8973ff45-fcf5-4b50-93f8-4068653efda0', 
  'p775917', 
  'Admin User', 
  'https://api.dicebear.com/7.x/avataaars/svg?seed=8973ff45-fcf5-4b50-93f8-4068653efda0',
  'admin'
) ON CONFLICT (id) DO UPDATE SET 
  role = 'admin',
  username = 'p775917',
  full_name = 'Admin User',
  updated_at = NOW();

-- Step 4: Create a function to update profile timestamps when role changes
CREATE OR REPLACE FUNCTION update_profile_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 5: Create trigger to update timestamp on any profile change
DROP TRIGGER IF EXISTS update_user_profiles_timestamp ON user_profiles;
CREATE TRIGGER update_user_profiles_timestamp
  BEFORE UPDATE ON user_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_profile_timestamp();

-- Step 6: Add updated_at column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'updated_at') THEN
        ALTER TABLE user_profiles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
END $$;

-- Step 7: Update all existing profiles to have updated_at timestamp
UPDATE user_profiles SET updated_at = NOW() WHERE updated_at IS NULL;

-- Step 8: Test the role update - this should work now
UPDATE user_profiles 
SET role = 'admin' 
WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';

-- Step 9: Verify everything is working
SELECT 'Current Profile Status:' as check_type;
SELECT 
  id,
  username,
  full_name,
  role,
  created_at,
  updated_at,
  CASE 
    WHEN role IN ('admin', 'editor') THEN 'HAS ADMIN ACCESS' 
    ELSE 'NO ADMIN ACCESS' 
  END as access_level
FROM user_profiles 
WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';

-- Step 10: Test role changes
SELECT 'Testing role changes...' as status;

-- Change to user role
UPDATE user_profiles SET role = 'user' WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';
SELECT 'After setting to user:' as test, role, updated_at FROM user_profiles WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';

-- Change to editor role  
UPDATE user_profiles SET role = 'editor' WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';
SELECT 'After setting to editor:' as test, role, updated_at FROM user_profiles WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';

-- Change back to admin role
UPDATE user_profiles SET role = 'admin' WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';
SELECT 'After setting to admin:' as test, role, updated_at FROM user_profiles WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';

-- Final verification
SELECT 'FINAL RESULT:' as status;
SELECT 
  'Your profile is ready!' as message,
  id,
  username, 
  role,
  CASE 
    WHEN role IN ('admin', 'editor') THEN '✅ HAS ADMIN ACCESS' 
    ELSE '❌ NO ADMIN ACCESS' 
  END as admin_status,
  updated_at
FROM user_profiles 
WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';

-- Instructions
SELECT '🔄 NEXT STEPS:' as instruction;
SELECT '1. Clear your browser cache completely' as step_1;
SELECT '2. Refresh the page' as step_2;
SELECT '3. Login again' as step_3;
SELECT '4. Check the debug panel - Profile Role should show: admin' as step_4;
SELECT '5. Try changing role in Supabase and click Refresh button in debug panel' as step_5;
