-- UNIVERSAL ROLE DROPDOWN FOR ALL USERS
-- This creates the role dropdown for ANY user profile in Supabase
-- Run this ENTIRE script in Supabase SQL Editor

-- Step 1: Create ENUM type for better UI support
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('user', 'writer', 'editor', 'admin');
    END IF;
END $$;

-- Step 2: Ensure user_profiles table exists with proper structure
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    avatar_url TEXT,
    role VARCHAR(20) DEFAULT 'user',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 3: Add check constraint for role dropdown (this is the key!)
ALTER TABLE user_profiles 
DROP CONSTRAINT IF EXISTS user_profiles_role_check;

ALTER TABLE user_profiles 
ADD CONSTRAINT user_profiles_role_check 
CHECK (role IN ('user', 'writer', 'editor', 'admin'));

-- Step 4: Add column comment to help Supabase UI
COMMENT ON COLUMN user_profiles.role IS 'User role: user, writer, editor, or admin';

-- Step 5: Create function to automatically create profiles for new users
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, username, full_name, avatar_url, role)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        COALESCE(NEW.raw_user_meta_data->>'avatar_url', 'https://api.dicebear.com/7.x/avataaars/svg?seed=' || NEW.id::text),
        'user'  -- Default role for new users
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 6: Create trigger for automatic profile creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Step 7: Enable Row Level Security
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Step 8: Create policies so admins can manage roles
CREATE POLICY "Users can view all profiles" ON user_profiles FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Admins can manage all profiles" ON user_profiles FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role IN ('admin', 'editor'))
);

-- Step 9: Show current profiles and test the dropdown
SELECT 'Current User Profiles:' as info;
SELECT 
  username,
  full_name,
  role,
  created_at,
  CASE 
    WHEN role = 'admin' THEN '👑 ADMIN'
    WHEN role = 'editor' THEN '✏️ EDITOR' 
    WHEN role = 'writer' THEN '📝 WRITER'
    ELSE '👤 USER' 
  END as role_display
FROM user_profiles 
ORDER BY created_at;

-- Step 10: Show valid role options
SELECT 'Valid Role Options for Dropdown:' as info;
SELECT unnest(enum_range(NULL::user_role)) as available_roles;

-- Success messages
SELECT '✅ UNIVERSAL ROLE DROPDOWN CREATED!' as result;
SELECT 'Now ANY user profile in Supabase will show role as dropdown!' as info1;
SELECT 'Go to Supabase → Table Editor → user_profiles → Click any role field' as instruction1;
SELECT 'You should see dropdown with: user, writer, editor, admin' as instruction2;
SELECT 'New users will automatically get profiles when they register!' as info2;
