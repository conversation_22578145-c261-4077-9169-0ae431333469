-- Comprehensive Sample Data for News Website
-- Run this in your Supabase SQL Editor after setting up the schema

-- First, create sample auth users and then their profiles
-- Note: This approach creates both auth users and profiles properly

-- Create sample auth users first (this requires admin privileges)
-- Since we can't directly insert into auth.users, we'll create profiles that can be linked later
-- For now, let's create articles with a system author approach

-- Create a system user profile for sample articles
DO $$
DECLARE
    system_user_id UUID := '00000000-0000-0000-0000-000000000000';
BEGIN
    -- Insert system user profile (this will be used for sample articles)
    INSERT INTO user_profiles (id, username, full_name, avatar_url, role)
    VALUES (system_user_id, 'system', 'System User', 'https://api.dicebear.com/7.x/avataaars/svg?seed=system', 'admin')
    ON CONFLICT (id) DO NOTHING;
END $$;

-- Insert sample articles using system user
INSERT INTO articles (id, title, summary, content, category, image_url, author_id, published_at, is_featured, is_breaking, is_trending, views) VALUES
-- Featured Articles
('a1111111-1111-1111-1111-111111111111', 'Global Climate Summit Reaches Historic Agreement', 'World leaders unite on ambitious climate targets for 2030, marking a turning point in international environmental policy.', '<p>In a groundbreaking development, world leaders at the Global Climate Summit have reached a historic agreement on ambitious climate targets for 2030. The agreement, signed by representatives from over 190 countries, marks a significant turning point in international environmental policy.</p><h2>Key Provisions</h2><p>The agreement includes commitments to reduce global carbon emissions by 50% by 2030, with developed nations pledging to achieve net-zero emissions by 2035. Additionally, a $500 billion fund has been established to support developing countries in their transition to renewable energy.</p><h2>Industry Response</h2><p>Major corporations have welcomed the agreement, with many announcing accelerated timelines for their own sustainability initiatives. The renewable energy sector is expected to see unprecedented growth as a result of these commitments.</p>', 'World', 'https://images.unsplash.com/photo-1569163139394-de4e4f43e4e5?w=800&h=600&fit=crop', '00000000-0000-0000-0000-000000000000', NOW() - INTERVAL '2 hours', true, false, true, 1250),

('a2222222-2222-2222-2222-222222222222', 'Revolutionary AI Breakthrough in Medical Diagnosis', 'New AI system achieves 99% accuracy in early cancer detection, potentially saving millions of lives worldwide.', '<p>Researchers at leading medical institutions have announced a revolutionary breakthrough in artificial intelligence that could transform medical diagnosis. The new AI system has achieved an unprecedented 99% accuracy rate in early cancer detection across multiple cancer types.</p><h2>How It Works</h2><p>The system uses advanced machine learning algorithms to analyze medical imaging data, including CT scans, MRIs, and X-rays. By processing thousands of images in seconds, it can identify subtle patterns that might be missed by human radiologists.</p><h2>Clinical Trials</h2><p>The technology has been tested in clinical trials involving over 10,000 patients across 15 countries. Results show that the AI system not only matches but often exceeds the diagnostic accuracy of experienced medical professionals.</p><p>Dr. Maria Rodriguez, lead researcher on the project, stated: "This breakthrough represents a paradigm shift in how we approach medical diagnosis. Early detection is crucial for successful treatment outcomes, and this technology could save millions of lives."</p>', 'Tech', 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop', '00000000-0000-0000-0000-000000000000', NOW() - INTERVAL '4 hours', true, false, false, 890),

('a3333333-3333-3333-3333-333333333333', 'Historic Peace Agreement Signed in Middle East', 'After decades of conflict, neighboring nations sign comprehensive peace treaty, opening new era of cooperation.', '<p>In a ceremony attended by world leaders and diplomats, two neighboring Middle Eastern nations have signed a comprehensive peace agreement, ending decades of conflict and opening a new era of cooperation and prosperity for the region.</p><h2>Terms of the Agreement</h2><p>The peace treaty includes provisions for economic cooperation, shared water resources management, and joint infrastructure projects. Both nations have committed to establishing diplomatic relations and opening their borders for trade and tourism.</p><h2>International Support</h2><p>The agreement has received widespread international support, with the United Nations, European Union, and major world powers pledging financial and technical assistance for the implementation of the peace process.</p><h2>Economic Impact</h2><p>Economists predict that the peace agreement could boost regional GDP by up to 15% over the next five years, as trade barriers are removed and joint development projects are initiated.</p>', 'Politics', 'https://images.unsplash.com/photo-1529107386315-e1a2ed48a620?w=800&h=600&fit=crop', '00000000-0000-0000-0000-000000000000', NOW() - INTERVAL '6 hours', true, false, false, 2100),

-- Breaking News
('a4444444-4444-4444-4444-444444444444', 'BREAKING: Major Earthquake Hits Pacific Region', 'Magnitude 7.8 earthquake strikes Pacific coast, tsunami warnings issued for multiple countries.', '<p><strong>BREAKING NEWS:</strong> A powerful magnitude 7.8 earthquake has struck the Pacific coast, prompting tsunami warnings for multiple countries in the region. Emergency services are responding to reports of damage and casualties.</p><h2>Current Situation</h2><p>The earthquake occurred at 3:45 AM local time, with its epicenter located approximately 50 kilometers off the coast. Coastal communities have been evacuated as a precautionary measure, and emergency shelters have been established.</p><h2>Response Efforts</h2><p>International rescue teams are being deployed to assist with search and rescue operations. The Red Cross and other humanitarian organizations are coordinating relief efforts for affected populations.</p><p><em>This is a developing story. Updates will be provided as more information becomes available.</em></p>', 'World', 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=800&h=600&fit=crop', '00000000-0000-0000-0000-000000000000', NOW() - INTERVAL '30 minutes', false, true, true, 3500),

-- Sports Articles
('a5555555-5555-5555-5555-555555555555', 'Olympic Games: Record-Breaking Performances Highlight Day 5', 'Athletes continue to push boundaries with multiple world records set across swimming and track events.', '<p>Day 5 of the Olympic Games has been marked by extraordinary athletic performances, with multiple world records broken across swimming and track and field events. The games continue to showcase the pinnacle of human athletic achievement.</p><h2>Swimming Records</h2><p>In the aquatic center, three world records were shattered in a single session. The mens 200m freestyle saw a time that many thought impossible, while the womens relay team set a new standard for teamwork and speed.</p><h2>Track and Field Excellence</h2><p>The athletics stadium witnessed equally impressive performances, with the mens javelin throw reaching a distance that rewrites the record books. The womens marathon also saw a new Olympic record, demonstrating the incredible endurance and determination of elite athletes.</p>', 'Sports', 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?w=800&h=600&fit=crop', '00000000-0000-0000-0000-000000000000', NOW() - INTERVAL '8 hours', false, false, true, 1800),

-- Entertainment Articles
('a6666666-6666-6666-6666-666666666666', 'Blockbuster Film Breaks Opening Weekend Records', 'Highly anticipated superhero movie shatters box office expectations with $300M global opening.', '<p>The latest installment in the beloved superhero franchise has exceeded all expectations, earning over $300 million worldwide in its opening weekend. The film has broken multiple box office records and received critical acclaim from audiences and critics alike.</p><h2>Box Office Success</h2><p>The movie earned $150 million domestically and $150 million internationally, making it the highest-grossing opening weekend for the franchise. Theater chains report sold-out screenings and extended showtimes to meet demand.</p><h2>Critical Reception</h2><p>Critics have praised the films storytelling, visual effects, and performances. The movie currently holds a 95% rating on review aggregation sites, with many calling it the best entry in the series.</p>', 'Entertainment', 'https://images.unsplash.com/photo-1489599904472-af35ff2c7c3f?w=800&h=600&fit=crop', '00000000-0000-0000-0000-000000000000', NOW() - INTERVAL '12 hours', false, false, false, 950)
ON CONFLICT (id) DO NOTHING;

-- Insert more articles to have enough content
INSERT INTO articles (title, summary, content, category, image_url, author_id, published_at, is_featured, is_breaking, is_trending, views) VALUES
('Tech Giants Announce Quantum Computing Partnership', 'Major technology companies join forces to accelerate quantum computing research and development.', '<p>Leading technology companies have announced a groundbreaking partnership to accelerate quantum computing research and development. This collaboration aims to overcome current technical challenges and bring quantum computing closer to practical applications.</p>', 'Tech', 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop', '00000000-0000-0000-0000-000000000000', NOW() - INTERVAL '1 day', false, false, true, 720),

('New Archaeological Discovery Rewrites Ancient History', 'Archaeologists uncover 5,000-year-old civilization that challenges our understanding of early human societies.', '<p>A team of international archaeologists has made a discovery that could rewrite our understanding of ancient civilizations. The newly uncovered site reveals evidence of a sophisticated society that existed 5,000 years ago.</p>', 'World', 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=800&h=600&fit=crop', '00000000-0000-0000-0000-000000000000', NOW() - INTERVAL '1 day', false, false, false, 540),

('Championship Final Set as Underdogs Advance', 'Surprising semifinal results set up an unexpected championship matchup between two underdog teams.', '<p>In one of the most surprising turns in recent sports history, two underdog teams have advanced to the championship final, defeating heavily favored opponents in dramatic semifinal matches.</p>', 'Sports', 'https://images.unsplash.com/photo-1579952363873-27d3bfad9c0d?w=800&h=600&fit=crop', '00000000-0000-0000-0000-000000000000', NOW() - INTERVAL '2 days', false, false, false, 1100),

('Celebrity Power Couple Announces Surprise Wedding', 'Hollywood stars surprise fans with intimate ceremony after secret engagement earlier this year.', '<p>In a surprise announcement that has delighted fans worldwide, two of Hollywoods biggest stars have revealed they were married in an intimate ceremony last weekend, following a secret engagement earlier this year.</p>', 'Entertainment', 'https://images.unsplash.com/photo-1511632765486-a01980e01a18?w=800&h=600&fit=crop', '00000000-0000-0000-0000-000000000000', NOW() - INTERVAL '3 days', false, false, false, 2200);

-- Insert sample comments (using system user for now)
INSERT INTO comments (article_id, user_id, content) VALUES
('a1111111-1111-1111-1111-111111111111', '00000000-0000-0000-0000-000000000000', 'This is exactly the kind of international cooperation we need to address climate change. Hopefully, this agreement will lead to real action.'),
('a2222222-2222-2222-2222-222222222222', '00000000-0000-0000-0000-000000000000', 'This AI breakthrough could revolutionize healthcare. Early detection saves lives!'),
('a3333333-3333-3333-3333-333333333333', '00000000-0000-0000-0000-000000000000', 'Finally, some positive news from the region. Peace agreements like this give hope for the future.');

-- Insert sample advertisements
INSERT INTO advertisements (title, description, image_url, link_url, position, is_active, created_by) VALUES
('Premium News Subscription', 'Get unlimited access to all premium articles and exclusive content', 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=300&h=250&fit=crop', '#subscribe', 'sidebar', true, '00000000-0000-0000-0000-000000000000'),
('Latest Tech Gadgets', 'Discover the newest technology products and innovations', 'https://images.unsplash.com/photo-1468495244123-6c6c332eeece?w=300&h=250&fit=crop', '#tech-store', 'sidebar', true, '00000000-0000-0000-0000-000000000000'),
('Investment Opportunities', 'Explore smart investment options for your financial future', 'https://images.unsplash.com/photo-1559526324-4b87b5e36e44?w=300&h=250&fit=crop', '#invest', 'sidebar', true, '00000000-0000-0000-0000-000000000000');

-- Insert newsletter subscribers
INSERT INTO newsletter_subscribers (email) VALUES
('<EMAIL>'),
('<EMAIL>'),
('<EMAIL>'),
('<EMAIL>'),
('<EMAIL>');

-- Insert website settings
INSERT INTO website_settings (setting_key, setting_value, setting_type, description, updated_by) VALUES
('site_title', 'THE SACH PATRA', 'text', 'Main website title', '00000000-0000-0000-0000-000000000000'),
('site_description', 'Your trusted source for breaking news and in-depth analysis', 'text', 'Website description for SEO', '00000000-0000-0000-0000-000000000000'),
('articles_per_page', '10', 'number', 'Number of articles to display per page', '00000000-0000-0000-0000-000000000000'),
('enable_comments', 'true', 'boolean', 'Enable/disable comments on articles', '00000000-0000-0000-0000-000000000000'),
('contact_email', '<EMAIL>', 'text', 'Contact email address', '00000000-0000-0000-0000-000000000000')
ON CONFLICT (setting_key) DO UPDATE SET
  setting_value = EXCLUDED.setting_value,
  updated_at = NOW();
